#!/usr/bin/env python3
"""
Enhanced SOIL 阶段1性能提取脚本

用于从训练日志中提取阶段1结束时的性能数据，支持批量分析和对比。
"""

import re
import sys
import os
import json
from datetime import datetime

def extract_stage1_performance(log_file):
    """提取阶段1结束时的性能数据"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading file {log_file}: {e}")
        return None
    
    # 查找epoch 19的性能数据
    stage1_end_pattern = r'epoch 19.*valid_score: ([\d.]+)'
    
    for i, line in enumerate(lines):
        if 'epoch 19' in line and 'valid_score' in line:
            valid_score_match = re.search(stage1_end_pattern, line)
            if valid_score_match and i+2 < len(lines):
                # 查找对应的valid result和test result
                valid_result_line = None
                test_result_line = None
                
                # 向后查找valid result和test result
                for j in range(i+1, min(i+5, len(lines))):
                    if 'valid result:' in lines[j]:
                        valid_result_line = lines[j]
                    elif 'test result:' in lines[j]:
                        test_result_line = lines[j]
                        break
                
                if valid_result_line and test_result_line:
                    # 提取详细指标
                    valid_metrics = extract_metrics(valid_result_line)
                    test_metrics = extract_metrics(test_result_line)
                    
                    return {
                        'epoch': 19,
                        'valid_score': float(valid_score_match.group(1)),
                        'valid_metrics': valid_metrics,
                        'test_metrics': test_metrics,
                        'log_file': log_file
                    }
    
    return None

def extract_metrics(result_line):
    """从结果行中提取所有指标"""
    metrics = {}
    
    # 定义指标模式
    patterns = {
        'recall@5': r'recall@5: ([\d.]+)',
        'recall@10': r'recall@10: ([\d.]+)',
        'recall@20': r'recall@20: ([\d.]+)',
        'recall@50': r'recall@50: ([\d.]+)',
        'ndcg@5': r'ndcg@5: ([\d.]+)',
        'ndcg@10': r'ndcg@10: ([\d.]+)',
        'ndcg@20': r'ndcg@20: ([\d.]+)',
        'ndcg@50': r'ndcg@50: ([\d.]+)',
        'precision@5': r'precision@5: ([\d.]+)',
        'precision@10': r'precision@10: ([\d.]+)',
        'precision@20': r'precision@20: ([\d.]+)',
        'precision@50': r'precision@50: ([\d.]+)',
    }
    
    for metric_name, pattern in patterns.items():
        match = re.search(pattern, result_line)
        if match:
            metrics[metric_name] = float(match.group(1))
    
    return metrics

def extract_training_info(log_file):
    """提取训练配置信息"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        return {}
    
    info = {}
    
    # 提取配置信息
    for line in lines[:50]:  # 只查看前50行
        if 'n_factors' in line:
            match = re.search(r'n_factors[:\s=]+(\d+)', line)
            if match:
                info['n_factors'] = int(match.group(1))
        
        if 'temperature' in line:
            match = re.search(r'temperature[:\s=]+([\d.]+)', line)
            if match:
                info['temperature'] = float(match.group(1))
        
        if 'attr_loss_weight' in line:
            match = re.search(r'attr_loss_weight[:\s=]+\[([\d.]+)\]', line)
            if match:
                info['attr_loss_weight'] = float(match.group(1))
        
        if 'factor_cl_weight' in line:
            match = re.search(r'factor_cl_weight[:\s=]+\[([\d.]+)\]', line)
            if match:
                info['factor_cl_weight'] = float(match.group(1))
    
    return info

def compare_with_baseline(result, baseline_recall_20=0.1013):
    """与基准性能对比"""
    if not result or 'valid_metrics' not in result:
        return None
    
    current_recall_20 = result['valid_metrics'].get('recall@20', 0)
    improvement = current_recall_20 - baseline_recall_20
    improvement_pct = (improvement / baseline_recall_20) * 100
    
    return {
        'current': current_recall_20,
        'baseline': baseline_recall_20,
        'improvement': improvement,
        'improvement_pct': improvement_pct
    }

def format_performance_report(result, training_info=None, comparison=None):
    """格式化性能报告"""
    if not result:
        return "无法提取性能数据"
    
    report = []
    report.append("=" * 60)
    report.append("Enhanced SOIL 阶段1性能报告")
    report.append("=" * 60)
    
    # 基本信息
    report.append(f"日志文件: {result['log_file']}")
    report.append(f"训练轮次: Epoch {result['epoch']}")
    report.append(f"验证分数: {result['valid_score']:.4f}")
    report.append("")
    
    # 训练配置
    if training_info:
        report.append("训练配置:")
        for key, value in training_info.items():
            report.append(f"  {key}: {value}")
        report.append("")
    
    # 验证集性能
    report.append("验证集性能:")
    valid_metrics = result['valid_metrics']
    report.append(f"  Recall@20: {valid_metrics.get('recall@20', 0):.4f}")
    report.append(f"  NDCG@20:   {valid_metrics.get('ndcg@20', 0):.4f}")
    report.append(f"  Precision@20: {valid_metrics.get('precision@20', 0):.4f}")
    report.append("")
    
    # 测试集性能
    report.append("测试集性能:")
    test_metrics = result['test_metrics']
    report.append(f"  Recall@20: {test_metrics.get('recall@20', 0):.4f}")
    report.append(f"  NDCG@20:   {test_metrics.get('ndcg@20', 0):.4f}")
    report.append(f"  Precision@20: {test_metrics.get('precision@20', 0):.4f}")
    report.append("")
    
    # 性能对比
    if comparison:
        report.append("性能对比 (vs 基准):")
        report.append(f"  当前 Recall@20: {comparison['current']:.4f}")
        report.append(f"  基准 Recall@20: {comparison['baseline']:.4f}")
        report.append(f"  绝对提升: {comparison['improvement']:+.4f}")
        report.append(f"  相对提升: {comparison['improvement_pct']:+.2f}%")
        
        if comparison['improvement'] > 0:
            report.append("  状态: ✅ 性能提升")
        elif comparison['improvement'] > -0.001:
            report.append("  状态: ➖ 性能持平")
        else:
            report.append("  状态: ❌ 性能下降")
    
    report.append("=" * 60)
    
    return "\n".join(report)

def batch_analysis(log_directory):
    """批量分析多个日志文件"""
    if not os.path.isdir(log_directory):
        print(f"目录不存在: {log_directory}")
        return
    
    results = []
    
    # 查找所有日志文件
    for filename in os.listdir(log_directory):
        if filename.endswith('.log'):
            log_path = os.path.join(log_directory, filename)
            result = extract_stage1_performance(log_path)
            if result:
                training_info = extract_training_info(log_path)
                comparison = compare_with_baseline(result)
                results.append({
                    'filename': filename,
                    'result': result,
                    'training_info': training_info,
                    'comparison': comparison
                })
    
    # 排序并显示结果
    results.sort(key=lambda x: x['comparison']['improvement'] if x['comparison'] else 0, reverse=True)
    
    print("批量分析结果:")
    print("=" * 80)
    
    for item in results:
        filename = item['filename']
        comparison = item['comparison']
        if comparison:
            print(f"{filename:30} | Recall@20: {comparison['current']:.4f} | 提升: {comparison['improvement_pct']:+.2f}%")
        else:
            print(f"{filename:30} | 无法提取性能数据")
    
    print("=" * 80)
    
    # 显示最佳结果的详细报告
    if results and results[0]['comparison']:
        print("\n最佳结果详细报告:")
        best_result = results[0]
        report = format_performance_report(
            best_result['result'], 
            best_result['training_info'], 
            best_result['comparison']
        )
        print(report)

def main():
    if len(sys.argv) < 2:
        print("用法:")
        print("  单文件分析: python extract_stage1_performance.py <log_file>")
        print("  批量分析:   python extract_stage1_performance.py <log_directory>")
        sys.exit(1)
    
    path = sys.argv[1]
    
    if os.path.isfile(path):
        # 单文件分析
        result = extract_stage1_performance(path)
        if result:
            training_info = extract_training_info(path)
            comparison = compare_with_baseline(result)
            report = format_performance_report(result, training_info, comparison)
            print(report)
        else:
            print("无法从日志文件中提取阶段1性能数据")
    
    elif os.path.isdir(path):
        # 批量分析
        batch_analysis(path)
    
    else:
        print(f"路径不存在: {path}")
        sys.exit(1)

if __name__ == "__main__":
    main()
