# Enhanced SOIL 优化配置文件
# 基于性能分析的优化建议

# 基础配置 (继承自原始SOIL.yaml)
gpu_id: 0
use_gpu: True
seed: [999]
data_path: ./data/
checkpoint_dir: saved
epochs: 1500  # 增加训练轮数
stopping_step: 15  # 增加早停patience
train_batch_size: 2048
eval_batch_size: 2048
learner: adam
eval_step: 1

# 数据集配置
inter_splitting_label: x_label
filter_out_cod_start_users: True
is_multimodal_model: True
save_recommended_topk: True
recommend_topk: recommend_topk/

# 评估配置
metrics: ['Recall', 'NDCG', 'Precision', 'MAP']
topk: [5, 10, 20, 50]
valid_metric: Recall@20
valid_metric_bigger: True

# 模型架构配置
embedding_size: 64
weight_decay: 0.001  # 增加正则化
req_training: True
training_neg_sample_num: 1
use_neg_sampling: True
use_full_sampling: False

# 多模态配置
use_raw_features: False
max_txt_len: 32
max_img_size: 256
vocab_size: 30522
type_vocab_size: 2
hidden_size: 4
pad_token_id: 0
max_position_embeddings: 512
layer_norm_eps: 1e-12
hidden_dropout_prob: 0.3  # 增加dropout

# SOIL特定配置
knn_i: 10
knn_a: 2
n_ui_layers: 2
n_layers: 1
lambda_coeff: 0.9
reg_weight: 0.0001
knn_k: 10
end2end: False

# 优化后的超参数配置
learning_rate: [0.0005, 0.001, 0.002]  # 多个学习率选择
learning_rate_scheduler: [0.96, 50]

# 原始SOIL超参数
cl_loss: [0.001, 0.01, 0.1]
cl_loss2: [0.001, 0.01, 0.1]

# 属性学习配置 (优化后)
enable_attribute_learning: True
n_factors: [2, 3, 4]  # 减少因子数量选择
temperature: 1.0

# 优化后的损失权重
attr_loss_weight: [0.1, 0.2, 0.3, 0.5]  # 增加权重范围
factor_cl_weight: [0.05, 0.1, 0.15, 0.2]  # 增加权重范围

# 超参数搜索配置
hyper_parameters: ['cl_loss', 'cl_loss2', 'attr_loss_weight', 'factor_cl_weight', 'learning_rate', 'n_factors', 'seed']

# 训练策略配置
training_strategy:
  # 两阶段训练
  stage1_epochs: 20  # 第一阶段只训练推荐任务
  stage2_epochs: 1480  # 第二阶段加入属性学习
  
  # 权重调度
  weight_scheduling:
    attr_loss_start: 0.01  # 属性损失起始权重
    attr_loss_end: 0.3     # 属性损失结束权重
    factor_cl_start: 0.005 # 因子对比学习起始权重
    factor_cl_end: 0.15    # 因子对比学习结束权重
    
  # 早停策略
  early_stopping:
    patience: 15
    min_delta: 0.001
    monitor: 'Recall@20'
    
# 模型简化选项
model_simplification:
  remove_factor_cl: False  # 是否移除因子对比学习
  simple_attention: False  # 是否使用简单注意力机制
  reduce_mlp_layers: False # 是否减少MLP层数

# 数据配置
inter_file_name: baby.inter
vision_feature_file: image_feat.npy
text_feature_file: text_feat.npy
user_graph_dict_file: user_graph_dict.npy

# 字段配置
NEG_PREFIX: neg__
USER_ID_FIELD: userID
ITEM_ID_FIELD: itemID
TIME_FIELD: timestamp
field_separator: "\t"

# 实验配置
experiment:
  name: "enhanced_soil_optimization"
  description: "基于性能分析的Enhanced SOIL优化实验"
  baseline_recall20: 0.0967  # 原始SOIL基准性能
  target_recall20: 0.097     # 目标性能
  
# 日志配置
logging:
  level: INFO
  save_detailed_metrics: True
  save_training_curves: True
  
# 优化实验计划
optimization_phases:
  phase1:
    name: "超参数快速扫描"
    duration: "3-5天"
    focus: ["attr_loss_weight", "factor_cl_weight", "learning_rate"]
    epochs_per_trial: 30
    
  phase2:
    name: "训练策略优化"
    duration: "5-7天"  
    focus: ["two_stage_training", "weight_scheduling"]
    epochs_per_trial: 50
    
  phase3:
    name: "模型架构调优"
    duration: "7-10天"
    focus: ["n_factors", "model_simplification"]
    epochs_per_trial: 100
