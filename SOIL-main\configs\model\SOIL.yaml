embedding_size: 64
n_ui_layers: 2
n_layers: 1

learning_rate_scheduler: [0.96, 50]
lambda_coeff: 0.9
reg_weight: 1e-04

knn_k: 10

learning_rate: 0.001

cl_loss:  [0.01]  # 使用最佳参数
cl_loss2: [0.01] # 使用最佳参数

# 属性驱动解耦学习参数
enable_attribute_learning: False  # 是否启用属性学习（默认关闭以保持兼容性）
n_factors: 4  # 因子数量
attr_loss_weight: [0.05]  # 属性损失权重 - 重新验证原权重配置
factor_cl_weight: [0.01]  # 因子对比学习权重 - 重新验证原权重配置
temperature: 1.0  # 对比学习温度参数

# 超参数配置：根据是否启用属性学习来决定调优参数
#hyper_parameters: ["cl_loss","cl_loss2"]  # 基础版本
# 当启用属性学习时，会自动添加 attr_loss_weight 和 factor_cl_weight 到超参数列表