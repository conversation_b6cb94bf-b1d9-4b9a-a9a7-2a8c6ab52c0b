# coding: utf-8

import os
import numpy as np
import scipy.sparse as sp
import torch
import torch.nn as nn
import torch.nn.functional as F

from common.abstract_recommender import GeneralRecommender
from utils.utils import build_sim, build_mixed_graph, build_non_zero_graph, build_knn_normalized_graph,build_graph_from_adj


class SOIL(GeneralRecommender):
    def __init__(self, config, dataset):
        super(SOIL, self).__init__(config, dataset)
        self.sparse = True
        self.cl_loss = config['cl_loss']
        self.cl_loss2 = config['cl_loss2']
        self.n_ui_layers = config['n_ui_layers']
        self.embedding_dim = config['embedding_size']
        self.knn_k = config['knn_k']
        self.n_layers = config['n_layers']
        self.reg_weight = config['reg_weight']
        self.knn_i = config['knn_i']
        self.knn_a = config['knn_a']

        # 新增：属性驱动解耦学习参数
        self.enable_attribute_learning = config['enable_attribute_learning'] if 'enable_attribute_learning' in config else False
        self.n_factors = config['n_factors'] if 'n_factors' in config else 4
        # 处理超参数列表，取第一个值作为默认值
        attr_loss_weight_config = config['attr_loss_weight'] if 'attr_loss_weight' in config else 0.1
        self.attr_loss_weight = attr_loss_weight_config[0] if isinstance(attr_loss_weight_config, list) else attr_loss_weight_config

        factor_cl_weight_config = config['factor_cl_weight'] if 'factor_cl_weight' in config else 0.05
        self.factor_cl_weight = factor_cl_weight_config[0] if isinstance(factor_cl_weight_config, list) else factor_cl_weight_config

        self.dataset_name = config['dataset'] if 'dataset' in config else 'baby'
        self.temperature = config['temperature'] if 'temperature' in config else 1.0

        # 支持自定义factor_dim参数
        if 'factor_dim' in config:
            self.factor_dim = config['factor_dim']

        # load dataset info
        self.interaction_matrix = dataset.inter_matrix(form='coo').astype(np.float32)

        self.user_embedding = nn.Embedding(self.n_users, self.embedding_dim)
        self.item_id_embedding = nn.Embedding(self.n_items, self.embedding_dim)
        nn.init.xavier_uniform_(self.user_embedding.weight)
        nn.init.xavier_uniform_(self.item_id_embedding.weight)

        dataset_path = os.path.abspath(config['data_path'] + config['dataset'])

        image_interest_file = os.path.join(dataset_path, 'image_interest_{}_{}.pt'.format(self.knn_k, self.knn_i))
        text_interest_file = os.path.join(dataset_path, 'text_interest_{}_{}.pt'.format(self.knn_k, self.knn_i))

        mm_attractive_file = os.path.join(dataset_path, 'mm_attractive_{}.pt'.format(self.knn_a))

        self.norm_adj = self.get_adj_mat()
        self.R = self.sparse_mx_to_torch_sparse_tensor(self.R).float().to(self.device)
        self.norm_adj = self.sparse_mx_to_torch_sparse_tensor(self.norm_adj).float().to(self.device)


        if self.v_feat is not None:
            self.image_embedding = nn.Embedding.from_pretrained(self.v_feat, freeze=True)

        if self.t_feat is not None:
            self.text_embedding = nn.Embedding.from_pretrained(self.t_feat, freeze=True)

        #interest-aware graph
        if os.path.exists(image_interest_file) and os.path.exists(text_interest_file):
            image_interest_adj = torch.load(image_interest_file)
            print(image_interest_file+" loaded!")
            text_interest_adj = torch.load(text_interest_file)
            print(text_interest_file+" loaded!")
        else:
            #interest graph
            image_adj = build_sim(self.image_embedding.weight.detach())
            text_adj = build_sim(self.text_embedding.weight.detach())
            image_interest = torch.zeros_like(image_adj)
            text_interest = torch.zeros_like(text_adj)
            for user, items in dataset.history_items_per_u.items():
                items = torch.tensor([i for i in items])
                _, cols1 = torch.topk(image_adj[items].sum(dim=0), self.knn_i)
                _, cols2 = torch.topk(text_adj[items].sum(dim=0), self.knn_i)
                cols = torch.cat([cols1, cols2]).unique()
                image_interest[items[:, None],cols] += image_adj[items[:, None],cols]
                text_interest[items[:, None],cols] += text_adj[items[:, None],cols]

            image_interest_adj = build_non_zero_graph(image_interest,is_sparse=self.sparse, norm_type='sym')
            text_interest_adj = build_non_zero_graph(text_interest, is_sparse=self.sparse, norm_type='sym')

            #similarity graph
            image_adj = build_knn_normalized_graph(image_adj, topk=self.knn_k, is_sparse=self.sparse, norm_type='sym')
            text_adj = build_knn_normalized_graph(text_adj, topk=self.knn_k, is_sparse=self.sparse, norm_type='sym')

            #mixed graph
            image_interest_adj = torch.add(image_interest_adj, image_adj)
            text_interest_adj = torch.add(text_interest_adj, text_adj)

            torch.save(image_interest_adj, image_interest_file)
            torch.save(text_interest_adj, text_interest_file)
            del image_adj, text_adj, image_interest, text_interest
            torch.cuda.empty_cache()

        self.image_interest_adj = image_interest_adj.cuda()
        self.text_interest_adj = text_interest_adj.cuda()


        #Second-Order graph
        if os.path.exists(mm_attractive_file) and os.path.exists(mm_attractive_file.replace('.pt','_R.pt')):
            mm_attractive_adj = torch.load(mm_attractive_file)
            print(mm_attractive_file + " loaded!")
            mm_attractive_adj_R = torch.load(mm_attractive_file.replace('.pt','_R.pt'))
            print(mm_attractive_file.replace('.pt','_R.pt') + " loaded!")
        else:
            image_adj = build_sim(self.image_embedding.weight.detach())
            text_adj = build_sim(self.text_embedding.weight.detach())
            mm_attractive = torch.zeros_like(self.norm_adj)
            mm_attractive_R = torch.zeros_like(self.R)

            for user, items in dataset.history_items_per_u.items():
                items = torch.tensor([i for i in items])
                k_num = self.knn_a + items.size(0)
                mm_sim = torch.multiply(image_adj[items], text_adj[items])
                mm_value, mm_indices = torch.topk(mm_sim, k_num, dim=-1)

                k_mm_value, k_mm_indices = torch.topk(mm_value.flatten(), k_num)

                mm_indices = mm_indices.flatten()[k_mm_indices]

                uid = torch.zeros_like(mm_indices).fill_(user)
                mm_sparse = torch.sparse_coo_tensor(torch.stack([uid, self.n_users+mm_indices]), k_mm_value, size=self.norm_adj.size())
                mm_sparse_t = torch.sparse_coo_tensor(torch.stack([self.n_users+mm_indices, uid]), k_mm_value, size=self.norm_adj.size())

                mm_R_sparse = torch.sparse_coo_tensor(torch.stack([uid, mm_indices]), k_mm_value, size=self.R.size())

                mm_attractive += mm_sparse
                mm_attractive += mm_sparse_t

                mm_attractive_R += mm_R_sparse


            mm_attractive_adj = build_graph_from_adj(mm_attractive,is_sparse=self.sparse, norm_type='sym',mask=False)
            mm_attractive_adj_R = build_graph_from_adj(mm_attractive_R,is_sparse=self.sparse, norm_type='sym',mask=False)
            torch.save(mm_attractive_adj, mm_attractive_file)
            torch.save(mm_attractive_adj_R, mm_attractive_file.replace('.pt','_R.pt'))

            del image_adj, text_adj, mm_attractive, mm_sparse, mm_sparse_t, mm_R_sparse, mm_attractive_R
            torch.cuda.empty_cache()

        self.mm_attractive_adj = mm_attractive_adj.cuda()
        self.mm_attractive_adj_R = mm_attractive_adj_R.cuda()
        # argumented graph
        self.norm_adj = torch.add(self.norm_adj, self.mm_attractive_adj/2)
        self.R = torch.add(self.R, self.mm_attractive_adj_R/2)


        if self.v_feat is not None:
            self.image_trs = nn.Linear(self.v_feat.shape[1], self.embedding_dim)
        if self.t_feat is not None:
            self.text_trs = nn.Linear(self.t_feat.shape[1], self.embedding_dim)

        self.softmax = nn.Softmax(dim=-1)

        self.attention = nn.Sequential(
            nn.Linear(self.embedding_dim, self.embedding_dim),
            nn.Tanh(),
            nn.Linear(self.embedding_dim, 1, bias=False)
        )

        self.map_v = nn.Sequential(
            nn.Linear(self.embedding_dim, self.embedding_dim),
            nn.Sigmoid()
        )

        self.map_t = nn.Sequential(
            nn.Linear(self.embedding_dim, self.embedding_dim),
            nn.Sigmoid()
        )

        self.tau = 0.5

        # 新增：属性驱动解耦学习相关网络层
        if self.enable_attribute_learning:
            self._init_attribute_networks()
            dataset_path = config['data_path'] + config['dataset']
            self._load_attribute_data(dataset_path)

    def _init_attribute_networks(self):
        """初始化属性驱动解耦学习相关的网络层"""
        # 支持自定义factor_dim，默认为embedding_dim // n_factors
        self.factor_dim = getattr(self, 'factor_dim', self.embedding_dim // self.n_factors)
        factor_dim = self.factor_dim

        # 多模态注意力网络
        self.modality_attention = nn.Sequential(
            nn.Linear(factor_dim, 3),
            nn.Tanh(),
            nn.Linear(3, 3),
            nn.Softmax(dim=1)
        )

        # 属性预测网络（根据数据集调整）
        if self.dataset_name == 'baby':
            # Baby数据集只有4个因子：price, salesrank, brand, user_preference
            self.price_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 5)
            )
            self.salesrank_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 5)
            )
            self.brand_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 663)  # Baby数据集品牌数
            )
        else:
            # 其他数据集有5个因子：price, salesrank, brand, category, user_preference
            self.price_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 5)
            )
            self.salesrank_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 5)
            )
            self.brand_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 2081 if self.dataset_name == 'sports' else 1288)
            )
            self.category_mlp = nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, 18 if self.dataset_name == 'sports' else 19)
            )

        # 因子级别的分类器（用于内部对比学习）
        self.factor_classifiers = nn.ModuleList([
            nn.Sequential(
                nn.LeakyReLU(negative_slope=0.2),
                nn.Dropout(0.2),
                nn.Linear(factor_dim, self.n_factors)
            ) for _ in range(4)  # user, item, text, visual
        ])

        self.ce_loss = nn.CrossEntropyLoss()

    def _load_attribute_data(self, dataset_path):
        """加载属性数据"""
        try:
            import pandas as pd
            attr_file = os.path.join(dataset_path, 'item_attribute_label.csv')
            if os.path.exists(attr_file):
                attr_df = pd.read_csv(attr_file)
                self.attribute_labels = {}

                # 构建物品ID到属性标签的映射
                for _, row in attr_df.iterrows():
                    item_id = row.get('itemID', None)
                    if item_id is not None:
                        self.attribute_labels[item_id] = {
                            'price_label': row.get('price_label', 0),
                            'salesrank_label': row.get('salesrank_label', 0),
                            'brand_label': row.get('brand_label', 0),
                            'category_label': row.get('category_label', 0) if self.dataset_name != 'baby' else 0
                        }
                print(f"Loaded attribute labels for {len(self.attribute_labels)} items")
            else:
                print(f"Attribute file not found: {attr_file}")
                self.attribute_labels = {}
        except Exception as e:
            print(f"Error loading attribute data: {e}")
            self.attribute_labels = {}

    def pre_epoch_processing(self):
        pass

    def get_adj_mat(self):
        adj_mat = sp.dok_matrix((self.n_users + self.n_items, self.n_users + self.n_items), dtype=np.float32)
        adj_mat = adj_mat.tolil()
        R = self.interaction_matrix.tolil()

        adj_mat[:self.n_users, self.n_users:] = R
        adj_mat[self.n_users:, :self.n_users] = R.T
        adj_mat = adj_mat.todok()

        def normalized_adj_single(adj):
            rowsum = np.array(adj.sum(1))

            d_inv = np.power(rowsum, -0.5).flatten()
            d_inv[np.isinf(d_inv)] = 0.
            d_mat_inv = sp.diags(d_inv)

            norm_adj = d_mat_inv.dot(adj_mat)
            norm_adj = norm_adj.dot(d_mat_inv)
            return norm_adj.tocoo()

        norm_adj_mat = normalized_adj_single(adj_mat)
        norm_adj_mat = norm_adj_mat.tolil()
        self.R = norm_adj_mat[:self.n_users, self.n_users:]
        return norm_adj_mat.tocsr()

    def sparse_mx_to_torch_sparse_tensor(self, sparse_mx):
        """Convert a scipy sparse matrix to a torch sparse tensor."""
        sparse_mx = sparse_mx.tocoo().astype(np.float32)
        indices = torch.from_numpy(np.vstack((sparse_mx.row, sparse_mx.col)).astype(np.int64))
        values = torch.from_numpy(sparse_mx.data)
        shape = torch.Size(sparse_mx.shape)
        return torch.sparse.FloatTensor(indices, values, shape)

    def forward(self, adj, train=False):
        if self.v_feat is not None:
            image_feats = self.image_trs(self.image_embedding.weight)
        if self.t_feat is not None:
            text_feats = self.text_trs(self.text_embedding.weight)

        # Feature ID Embedding
        image_item_embeds = torch.multiply(self.item_id_embedding.weight, self.map_v(image_feats))
        text_item_embeds = torch.multiply(self.item_id_embedding.weight, self.map_t(text_feats))

        # 新增：属性驱动的因子分解
        if self.enable_attribute_learning and train:
            return self._forward_with_attribute_learning(adj, image_item_embeds, text_item_embeds)
        else:
            return self._forward_original(adj, image_item_embeds, text_item_embeds, train)

    def _forward_original(self, adj, image_item_embeds, text_item_embeds, train=False):
        """原始SOIL的前向传播逻辑"""
        # Second-Order Graph Convolution
        item_embeds = self.item_id_embedding.weight
        user_embeds = self.user_embedding.weight
        ego_embeddings = torch.cat([user_embeds, item_embeds], dim=0)
        all_embeddings = [ego_embeddings]
        for i in range(self.n_ui_layers):
            side_embeddings = torch.sparse.mm(adj, ego_embeddings)
            ego_embeddings = side_embeddings
            all_embeddings += [ego_embeddings]
        all_embeddings = torch.stack(all_embeddings, dim=1)
        all_embeddings = all_embeddings.mean(dim=1, keepdim=False)
        content_embeds = all_embeddings

        # Interest-Aware Item Graph Convolution
        for i in range(self.n_layers):
            image_item_embeds = torch.sparse.mm(self.image_interest_adj, image_item_embeds)
        image_user_embeds = torch.sparse.mm(self.R, image_item_embeds)
        image_embeds = torch.cat([image_user_embeds, image_item_embeds], dim=0)

        for i in range(self.n_layers):
            text_item_embeds = torch.sparse.mm(self.text_interest_adj, text_item_embeds)
        text_user_embeds = torch.sparse.mm(self.R, text_item_embeds)
        text_embeds = torch.cat([text_user_embeds, text_item_embeds], dim=0)

        # Attention Fuser
        att_common = torch.cat([self.attention(image_embeds), self.attention(text_embeds)], dim=-1)
        weight_common = self.softmax(att_common)
        common_embeds = weight_common[:, 0].unsqueeze(dim=1) * image_embeds + weight_common[:, 1].unsqueeze(
            dim=1) * text_embeds
        side_embeds = (image_embeds + text_embeds - common_embeds) / 3

        all_embeds = content_embeds + side_embeds

        all_embeddings_users, all_embeddings_items = torch.split(all_embeds, [self.n_users, self.n_items], dim=0)

        if train:
            return all_embeddings_users, all_embeddings_items, side_embeds, content_embeds

        return all_embeddings_users, all_embeddings_items

    def _forward_with_attribute_learning(self, adj, image_item_embeds, text_item_embeds):
        """融合属性驱动解耦学习的前向传播"""
        # 原始SOIL的图卷积部分
        item_embeds = self.item_id_embedding.weight
        user_embeds = self.user_embedding.weight
        ego_embeddings = torch.cat([user_embeds, item_embeds], dim=0)
        all_embeddings = [ego_embeddings]
        for i in range(self.n_ui_layers):
            side_embeddings = torch.sparse.mm(adj, ego_embeddings)
            ego_embeddings = side_embeddings
            all_embeddings += [ego_embeddings]
        all_embeddings = torch.stack(all_embeddings, dim=1)
        all_embeddings = all_embeddings.mean(dim=1, keepdim=False)
        content_embeds = all_embeddings

        # 因子分解 - 支持自定义factor_dim
        factor_dim = getattr(self, 'factor_dim', self.embedding_dim // self.n_factors)
        content_embeds_users, content_embeds_items = torch.split(content_embeds, [self.n_users, self.n_items], dim=0)

        # 分解用户、物品、文本、视觉嵌入为因子
        user_factors = torch.split(content_embeds_users, factor_dim, dim=1)
        item_factors = torch.split(content_embeds_items, factor_dim, dim=1)
        text_factors = torch.split(text_item_embeds, factor_dim, dim=1)
        visual_factors = torch.split(image_item_embeds, factor_dim, dim=1)

        # 属性感知的多模态融合
        fused_factors = []
        for i in range(self.n_factors):
            # 为每个因子计算多模态注意力权重
            factor_input = item_factors[i]
            weights = self.modality_attention(factor_input)

            # 融合多模态信息
            fused_factor = (weights[:, 0].unsqueeze(1) * F.normalize(item_factors[i], p=2, dim=1) +
                           weights[:, 1].unsqueeze(1) * F.normalize(text_factors[i], p=2, dim=1) +
                           weights[:, 2].unsqueeze(1) * F.normalize(visual_factors[i], p=2, dim=1))
            fused_factors.append(fused_factor)

        # 重构最终嵌入
        fused_item_embeds = torch.cat(fused_factors, dim=1)

        # Interest-Aware Item Graph Convolution (保持原有逻辑)
        for i in range(self.n_layers):
            image_item_embeds = torch.sparse.mm(self.image_interest_adj, image_item_embeds)
        image_user_embeds = torch.sparse.mm(self.R, image_item_embeds)
        image_embeds = torch.cat([image_user_embeds, image_item_embeds], dim=0)

        for i in range(self.n_layers):
            text_item_embeds = torch.sparse.mm(self.text_interest_adj, text_item_embeds)
        text_user_embeds = torch.sparse.mm(self.R, text_item_embeds)
        text_embeds = torch.cat([text_user_embeds, text_item_embeds], dim=0)

        # Attention Fuser
        att_common = torch.cat([self.attention(image_embeds), self.attention(text_embeds)], dim=-1)
        weight_common = self.softmax(att_common)
        common_embeds = weight_common[:, 0].unsqueeze(dim=1) * image_embeds + weight_common[:, 1].unsqueeze(
            dim=1) * text_embeds
        side_embeds = (image_embeds + text_embeds - common_embeds) / 3

        all_embeds = content_embeds + side_embeds
        all_embeddings_users, all_embeddings_items = torch.split(all_embeds, [self.n_users, self.n_items], dim=0)

        # 返回额外的因子信息用于损失计算
        return (all_embeddings_users, all_embeddings_items, side_embeds, content_embeds,
                user_factors, item_factors, text_factors, visual_factors, fused_factors)

    def bpr_loss(self, users, pos_items, neg_items):
        pos_scores = torch.sum(torch.mul(users, pos_items), dim=1)
        neg_scores = torch.sum(torch.mul(users, neg_items), dim=1)

        maxi = F.logsigmoid(pos_scores - neg_scores)
        bpr_loss = -torch.mean(maxi)

        return bpr_loss

    def InfoNCE(self, view1, view2, temperature):
        view1, view2 = F.normalize(view1, dim=1), F.normalize(view2, dim=1)
        pos_score = (view1 * view2).sum(dim=-1)
        pos_score = torch.exp(pos_score / temperature)
        ttl_score = torch.matmul(view1, view2.transpose(0, 1))
        ttl_score = torch.exp(ttl_score / temperature).sum(dim=1)
        cl_loss = -torch.log(pos_score / ttl_score)
        return torch.mean(cl_loss)

    def _compute_attribute_loss(self, fused_factors, pos_items):
        """计算属性预测损失"""
        if not hasattr(self, 'attribute_labels') or len(self.attribute_labels) == 0:
            return torch.tensor(0.0, device=fused_factors[0].device)

        attr_loss = 0.0
        valid_items = []
        price_labels, salesrank_labels, brand_labels, category_labels = [], [], [], []

        # 收集有效的属性标签
        for item_id in pos_items.cpu().numpy():
            if item_id in self.attribute_labels:
                valid_items.append(item_id)
                attr = self.attribute_labels[item_id]
                price_labels.append(attr['price_label'])
                salesrank_labels.append(attr['salesrank_label'])
                brand_labels.append(attr['brand_label'])
                if self.dataset_name != 'baby':
                    category_labels.append(attr['category_label'])

        if len(valid_items) == 0:
            return torch.tensor(0.0, device=fused_factors[0].device)

        # 获取对应物品的因子
        valid_indices = [i for i, item_id in enumerate(pos_items.cpu().numpy()) if item_id in self.attribute_labels]

        if len(valid_indices) > 0:
            # 价格预测损失
            price_logits = self.price_mlp(fused_factors[0][valid_indices])
            price_targets = torch.tensor(price_labels, device=price_logits.device, dtype=torch.long)
            attr_loss += self.ce_loss(price_logits, price_targets)

            # 销售排名预测损失
            salesrank_logits = self.salesrank_mlp(fused_factors[1][valid_indices])
            salesrank_targets = torch.tensor(salesrank_labels, device=salesrank_logits.device, dtype=torch.long)
            attr_loss += self.ce_loss(salesrank_logits, salesrank_targets)

            # 品牌预测损失
            brand_logits = self.brand_mlp(fused_factors[2][valid_indices])
            brand_targets = torch.tensor(brand_labels, device=brand_logits.device, dtype=torch.long)
            attr_loss += self.ce_loss(brand_logits, brand_targets)

            # 类别预测损失（如果适用）
            if self.dataset_name != 'baby' and len(category_labels) > 0:
                category_logits = self.category_mlp(fused_factors[3][valid_indices])
                category_targets = torch.tensor(category_labels, device=category_logits.device, dtype=torch.long)
                attr_loss += self.ce_loss(category_logits, category_targets)

        return attr_loss

    def _compute_factor_contrastive_loss(self, user_factors, item_factors, text_factors, visual_factors, users, pos_items):
        """计算因子级别的对比学习损失"""
        factor_cl_loss = 0.0
        batch_size = len(users)

        # 高级内部对比学习：确保同一因子在不同模态间的一致性
        for i in range(self.n_factors):
            # 用户因子分类
            user_logits = self.factor_classifiers[0](user_factors[i][users])
            user_targets = torch.full((batch_size,), i, device=user_logits.device, dtype=torch.long)
            factor_cl_loss += self.ce_loss(user_logits, user_targets)

            # 物品因子分类
            item_logits = self.factor_classifiers[1](item_factors[i][pos_items])
            item_targets = torch.full((batch_size,), i, device=item_logits.device, dtype=torch.long)
            factor_cl_loss += self.ce_loss(item_logits, item_targets)

            # 文本因子分类
            text_logits = self.factor_classifiers[2](text_factors[i][pos_items])
            text_targets = torch.full((batch_size,), i, device=text_logits.device, dtype=torch.long)
            factor_cl_loss += self.ce_loss(text_logits, text_targets)

            # 视觉因子分类
            visual_logits = self.factor_classifiers[3](visual_factors[i][pos_items])
            visual_targets = torch.full((batch_size,), i, device=visual_logits.device, dtype=torch.long)
            factor_cl_loss += self.ce_loss(visual_logits, visual_targets)

        # 高级间对比学习：确保不同因子间的独立性
        inter_cl_loss = 0.0
        for i in range(self.n_factors):
            for j in range(self.n_factors):
                if i != j:
                    # 物品-文本因子对比
                    inter_cl_loss += self._attribute_nce([item_factors[k][pos_items] for k in range(self.n_factors)],
                                                        [text_factors[k][pos_items] for k in range(self.n_factors)], i)
                    # 物品-视觉因子对比
                    inter_cl_loss += self._attribute_nce([item_factors[k][pos_items] for k in range(self.n_factors)],
                                                        [visual_factors[k][pos_items] for k in range(self.n_factors)], i)
                    # 文本-视觉因子对比
                    inter_cl_loss += self._attribute_nce([text_factors[k][pos_items] for k in range(self.n_factors)],
                                                        [visual_factors[k][pos_items] for k in range(self.n_factors)], i)

        return factor_cl_loss + inter_cl_loss

    def _attribute_nce(self, feats_t, feats_s, target_factor):
        """属性级别的NCE损失"""
        batch_size = feats_t[0].shape[0]
        feats_t_matrix = torch.stack(feats_t, dim=1)  # [batch_size, n_factors, factor_dim]

        logits = torch.bmm(feats_t_matrix, feats_s[target_factor].unsqueeze(-1)).squeeze(-1)  # [batch_size, n_factors]
        targets = torch.full((batch_size,), target_factor, device=logits.device, dtype=torch.long)

        return self.ce_loss(logits / self.temperature, targets)

    def calculate_loss(self, interaction):
        users = interaction[0]
        pos_items = interaction[1]
        neg_items = interaction[2]

        # 根据是否启用属性学习选择不同的前向传播
        forward_result = self.forward(self.norm_adj, train=True)

        if self.enable_attribute_learning and len(forward_result) == 9:  # 属性学习模式
            (ua_embeddings, ia_embeddings, side_embeds, content_embeds,
             user_factors, item_factors, text_factors, visual_factors, fused_factors) = forward_result
        else:  # 原始模式或兼容模式
            ua_embeddings, ia_embeddings, side_embeds, content_embeds = forward_result[:4]

        u_g_embeddings = ua_embeddings[users]
        pos_i_g_embeddings = ia_embeddings[pos_items]
        neg_i_g_embeddings = ia_embeddings[neg_items]

        # 基础BPR损失
        bpr_loss = self.bpr_loss(u_g_embeddings, pos_i_g_embeddings, neg_i_g_embeddings)

        side_embeds_users, side_embeds_items = torch.split(side_embeds, [self.n_users, self.n_items], dim=0)
        content_embeds_user, content_embeds_items = torch.split(content_embeds, [self.n_users, self.n_items], dim=0)

        # 原始对比学习损失
        cl_loss = self.InfoNCE(side_embeds_items[pos_items], content_embeds_items[pos_items], 0.2) + self.InfoNCE(
            side_embeds_users[users], content_embeds_user[users], 0.2)
        cl_loss2 = self.InfoNCE(u_g_embeddings, content_embeds_items[pos_items], 0.2) + self.InfoNCE(
            u_g_embeddings, side_embeds_items[pos_items], 0.2)

        total_loss = bpr_loss + self.cl_loss * cl_loss + self.cl_loss2 * cl_loss2

        # 新增：属性驱动的损失
        if self.enable_attribute_learning and len(forward_result) == 9:
            # 属性预测损失
            attr_loss = self._compute_attribute_loss(fused_factors, pos_items)

            # 因子级对比学习损失
            factor_cl_loss = self._compute_factor_contrastive_loss(
                user_factors, item_factors, text_factors, visual_factors, users, pos_items)

            total_loss += self.attr_loss_weight * attr_loss + self.factor_cl_weight * factor_cl_loss

        return total_loss

    def full_sort_predict(self, interaction):
        user = interaction[0]

        restore_user_e, restore_item_e = self.forward(self.norm_adj)
        u_embeddings = restore_user_e[user]

        # dot with all item embedding to accelerate
        scores = torch.matmul(u_embeddings, restore_item_e.transpose(0, 1))
        return scores
