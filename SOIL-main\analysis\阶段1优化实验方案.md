# Enhanced SOIL 阶段1优化实验方案

## 📋 **方案概述**

基于对Enhanced SOIL阶段1真实情况的重要技术澄清，本方案旨在优化阶段1的性能表现。阶段1并非"原版SOIL"，而是Enhanced SOIL架构的**部分激活状态**，包含因子分解、多模态注意力、隐式属性学习等核心组件，具有显著的优化潜力。

**优化目标**: 将阶段1的Valid Recall@20从当前的0.1013提升到0.1020-0.1025 (0.5-1.0%提升空间)

---

## 🎯 **技术背景与优化基础**

### **阶段1的实际架构组成**

#### **✅ 始终激活的组件**
1. **因子分解机制**: n_factors=3的因子分类器参与所有前向传播
2. **多模态注意力融合**: 文本、视觉、物品因子的注意力权重计算
3. **属性预测模块**: price_mlp、salesrank_mlp、brand_mlp仍在计算
4. **隐式属性学习**: 通过梯度反传影响模型参数

#### **🔄 损失函数状态**
```python
# 阶段1损失计算
total_loss = bpr_loss + self.cl_loss * cl_loss + self.cl_loss2 * cl_loss2
             + 0.0 * attr_loss + 0.0 * factor_cl_loss
```

**关键洞察**: attr_loss和factor_cl_loss仍被计算，只是权重为0，这些计算过程中的梯度仍会影响模型参数。

### **当前性能基准**
- **Valid Recall@20**: 0.1013 (epoch 19)
- **Test Recall@20**: 0.1029 (epoch 19)
- **训练损失**: 19.6245 (稳定收敛)
- **与原版SOIL对比**: -1.5% (已非常接近)

---

## 🚀 **三级优化策略**

## 优先级1: 因子分解相关参数优化 ⭐⭐⭐⭐⭐

### **优化目标**
增强因子分解机制的表示学习能力，优化因子间的协同效应。

### **技术原理**
因子分解是Enhanced SOIL的核心创新，通过优化因子相关参数可以直接提升表示质量。

### **实验设计**

#### **实验1.1: 因子维度优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第200-250行)
参数调整:
  factor_dim: [16, 21, 24, 28]  # 当前为21 (64/3)

实验配置:
  n_factors: 3  # 保持不变
  temperature: 0.5  # 保持不变

评估指标:
  primary: Valid Recall@20 (epoch 19)
  target: > 0.1018

预期提升: 0.2-0.3%
实验时间: 4个实验 × 20 epochs ≈ 3-4小时
```

#### **实验1.2: 因子分类器架构优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第150-180行)
当前架构: Sequential(LeakyReLU, Dropout(0.2), Linear(21→3))

优化方案:
  方案A: Sequential(Linear(21→32), BatchNorm1d, LeakyReLU, Dropout(0.1), Linear(32→3))
  方案B: Sequential(Linear(21→16), LeakyReLU, Dropout(0.15), Linear(16→3))
  方案C: Sequential(LeakyReLU, Dropout(0.1), Linear(21→8), LeakyReLU, Linear(8→3))

评估指标:
  target: > 0.1020

预期提升: 0.3-0.4%
实验时间: 3个实验 × 20 epochs ≈ 2-3小时
```

#### **实验1.3: 因子融合策略优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第400-411行)
当前融合: 加权平均 (weights[:, 0] * item + weights[:, 1] * text + weights[:, 2] * visual)

优化方案:
  方案A: 添加残差连接
  方案B: 使用门控机制
  方案C: 引入因子间交互

代码示例:
  # 方案A: 残差连接
  fused_factor = base_factor + (weights[:, 0] * item + weights[:, 1] * text + weights[:, 2] * visual)

  # 方案B: 门控机制
  gate = torch.sigmoid(self.gate_mlp(factor_input))
  fused_factor = gate * weighted_sum + (1-gate) * base_factor

预期提升: 0.2-0.4%
实验时间: 3个实验 × 20 epochs ≈ 2-3小时
```

---

## 优先级2: 多模态注意力机制优化 ⭐⭐⭐⭐

### **优化目标**
增强多模态特征融合效果，优化注意力权重的计算和分配。

### **技术原理**
多模态注意力机制决定了文本、视觉、物品特征的融合质量，优化注意力计算可以提升特征表示。

### **实验设计**

#### **实验2.1: 注意力温度参数优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第402行)
当前实现: weights = self.modality_attention(factor_input)

优化方案:
  添加温度参数: weights = F.softmax(self.modality_attention(factor_input) / temperature, dim=1)

参数范围:
  attention_temperature: [0.5, 0.8, 1.0, 1.2, 1.5]

评估指标:
  target: > 0.1018

预期提升: 0.1-0.3%
实验时间: 5个实验 × 20 epochs ≈ 3-4小时
```

#### **实验2.2: 注意力机制架构优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第130-140行)
当前架构: 简单的线性注意力

优化方案:
  方案A: 多头注意力机制
  方案B: 自注意力 + 交叉注意力
  方案C: 层次化注意力

代码示例:
  # 方案A: 多头注意力
  class MultiHeadModalityAttention(nn.Module):
      def __init__(self, input_dim, num_heads=4):
          self.num_heads = num_heads
          self.attention_heads = nn.ModuleList([
              nn.Linear(input_dim, 3) for _ in range(num_heads)
          ])

      def forward(self, x):
          head_outputs = [F.softmax(head(x), dim=1) for head in self.attention_heads]
          return torch.mean(torch.stack(head_outputs), dim=0)

预期提升: 0.2-0.4%
实验时间: 3个实验 × 20 epochs ≈ 2-3小时
```

#### **实验2.3: 模态权重平衡优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第405-407行)
当前实现: 等权重处理三个模态

优化方案:
  添加可学习的模态重要性权重

代码实现:
  # 添加模态权重参数
  self.modality_importance = nn.Parameter(torch.ones(3))

  # 在融合时应用
  importance_weights = F.softmax(self.modality_importance, dim=0)
  fused_factor = (importance_weights[0] * weights[:, 0] * item_factors +
                  importance_weights[1] * weights[:, 1] * text_factors +
                  importance_weights[2] * weights[:, 2] * visual_factors)

预期提升: 0.1-0.2%
实验时间: 1个实验 × 20 epochs ≈ 1小时
```

---

## 优先级3: 隐式属性学习增强 ⭐⭐⭐

### **优化目标**
在不直接优化属性损失的情况下，增强属性相关的隐式学习效果。

### **技术原理**
虽然属性损失权重为0，但属性预测模块仍在计算，可以通过优化这些模块的架构来增强隐式学习。

### **实验设计**

#### **实验3.1: 属性预测模块架构优化**
```yaml
# 修改文件: SOIL-main/models/soil.py (第100-120行)
当前架构: Sequential(LeakyReLU, Dropout(0.2), Linear(16→5))

优化方案:
  方案A: 增加中间层
  方案B: 添加BatchNorm
  方案C: 调整激活函数

代码示例:
  # 方案A: 增加中间层
  self.price_mlp = nn.Sequential(
      nn.Linear(factor_dim, 32),
      nn.BatchNorm1d(32),
      nn.LeakyReLU(),
      nn.Dropout(0.1),
      nn.Linear(32, 16),
      nn.LeakyReLU(),
      nn.Dropout(0.1),
      nn.Linear(16, 5)
  )

预期提升: 0.1-0.2%
实验时间: 3个实验 × 20 epochs ≈ 2-3小时
```

#### **实验3.2: 隐式正则化策略**
```yaml
# 修改文件: SOIL-main/models/soil.py (第593-601行)
优化思路: 虽然不直接优化属性损失，但可以添加隐式正则化

实现方案:
  在阶段1添加轻微的属性一致性正则化

代码实现:
  # 在calculate_loss中添加
  if epoch < 20:  # 阶段1
      # 添加隐式属性一致性损失 (权重很小)
      attr_consistency_loss = self._compute_attribute_consistency(fused_factors)
      total_loss += 0.001 * attr_consistency_loss  # 很小的权重

预期提升: 0.05-0.15%
实验时间: 2个实验 × 20 epochs ≈ 1-2小时
```

---

## 📊 **实验执行计划**

### **阶段1: 因子分解优化 (第1-2周)**
```
Week 1:
- 实验1.1: 因子维度优化 (3-4小时)
- 实验1.2: 因子分类器架构优化 (2-3小时)

Week 2:
- 实验1.3: 因子融合策略优化 (2-3小时)
- 结果分析和最优配置确定 (1天)
```

### **阶段2: 注意力机制优化 (第3周)**
```
Week 3:
- 实验2.1: 注意力温度参数优化 (3-4小时)
- 实验2.2: 注意力架构优化 (2-3小时)
- 实验2.3: 模态权重平衡优化 (1小时)
```

### **阶段3: 隐式学习增强 (第4周)**
```
Week 4:
- 实验3.1: 属性预测模块优化 (2-3小时)
- 实验3.2: 隐式正则化策略 (1-2小时)
- 最终整合和验证 (1-2天)
```

### **总时间投入**
- **实验时间**: 15-20小时
- **分析时间**: 3-4天
- **总周期**: 4周

---

## 🎯 **性能目标与评估**

### **分阶段目标**
| 阶段 | 目标指标 | 当前基准 | 预期提升 | 成功标准 |
|------|----------|----------|----------|----------|
| **阶段1** | Valid Recall@20 | 0.1013 | +0.3-0.5% | > 0.1018 |
| **阶段2** | Valid Recall@20 | 0.1018 | +0.2-0.3% | > 0.1021 |
| **阶段3** | Valid Recall@20 | 0.1021 | +0.1-0.2% | > 0.1023 |
| **整体目标** | Valid Recall@20 | 0.1013 | +0.6-1.0% | > 0.1023 |

### **对整体两阶段训练的影响预期**
- **当前最终性能**: Test Recall@20 = 0.1040
- **优化后预期**: Test Recall@20 = 0.1045-0.1050
- **相对原版SOIL**: 从+1.2%提升到+1.7-2.1%

---

## ⚠️ **风险控制与兼容性保证**

### **兼容性要求**
1. **保持n_factors=3**: 已验证的最优配置
2. **保持temperature=0.5**: 当前最优温度参数
3. **不影响阶段2**: 确保优化不破坏属性学习效果

### **风险缓解策略**
1. **渐进式优化**: 每次只改变一个组件
2. **回滚机制**: 保留每个实验的最优配置
3. **性能监控**: 密切监控阶段2的性能变化

### **实验终止条件**
1. **性能下降**: 如果优化导致性能下降超过0.1%
2. **训练不稳定**: 如果出现训练发散或收敛困难
3. **时间超限**: 如果单个实验超过预期时间50%

---

## 🛠️ **技术实现指导**

### **代码修改位置映射**

#### **核心文件清单**
```
SOIL-main/models/soil.py          # 主要模型实现
SOIL-main/configs/model/SOIL.yaml # 配置参数
SOIL-main/common/trainer.py       # 训练逻辑 (两阶段策略)
```

#### **关键代码位置**
```python
# 1. 因子分解相关 (SOIL-main/models/soil.py)
第150-180行: 因子分类器定义
第200-250行: 因子维度和初始化
第400-411行: 因子融合逻辑

# 2. 多模态注意力 (SOIL-main/models/soil.py)
第130-140行: 注意力机制定义
第402行: 注意力权重计算
第405-407行: 多模态融合

# 3. 属性预测模块 (SOIL-main/models/soil.py)
第100-120行: 属性预测MLP定义
第456-504行: 属性损失计算
第593-601行: 损失函数整合

# 4. 训练策略 (SOIL-main/common/trainer.py)
第220-235行: 两阶段权重切换逻辑
```

### **实验执行模板**

#### **配置文件修改模板**
```yaml
# SOIL-main/configs/model/SOIL.yaml
# 保持不变的核心参数
n_factors: 3
temperature: 0.5
attr_loss_weight: [0.05]
factor_cl_weight: [0.01]

# 实验参数 (根据具体实验调整)
factor_dim: 21              # 实验1.1调整
attention_temperature: 1.0  # 实验2.1调整
modality_importance: true   # 实验2.3添加
```

#### **实验运行命令**
```bash
# 基础运行命令
cd SOIL-main
python main.py -m SOIL -d baby --enable_attr

# 带参数覆盖的运行命令 (如需要)
python main.py -m SOIL -d baby --enable_attr --factor_dim 24

# 批量实验脚本示例
for factor_dim in 16 21 24 28; do
    echo "Testing factor_dim=$factor_dim"
    python main.py -m SOIL -d baby --enable_attr --factor_dim $factor_dim
    mv log/SOIL-baby-*.log log/factor_dim_${factor_dim}.log
done
```

### **性能监控脚本**

#### **阶段1性能提取脚本**
```python
# 创建文件: SOIL-main/analysis/extract_stage1_performance.py
import re
import sys

def extract_stage1_performance(log_file):
    """提取阶段1结束时的性能数据"""
    with open(log_file, 'r') as f:
        lines = f.readlines()

    stage1_end_pattern = r'epoch 19.*valid_score: ([\d.]+)'
    test_pattern = r'test result:.*recall@20: ([\d.]+)'

    for i, line in enumerate(lines):
        if 'epoch 19' in line and 'valid_score' in line:
            valid_score = re.search(stage1_end_pattern, line)
            if valid_score and i+2 < len(lines):
                test_line = lines[i+2]
                test_score = re.search(test_pattern, test_line)
                if test_score:
                    return {
                        'valid_recall_20': float(valid_score.group(1)),
                        'test_recall_20': float(test_score.group(1)),
                        'epoch': 19
                    }
    return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract_stage1_performance.py <log_file>")
        sys.exit(1)

    result = extract_stage1_performance(sys.argv[1])
    if result:
        print(f"Stage 1 Performance:")
        print(f"Valid Recall@20: {result['valid_recall_20']:.4f}")
        print(f"Test Recall@20: {result['test_recall_20']:.4f}")
    else:
        print("Could not extract stage 1 performance")
```

### **实验结果记录模板**

#### **实验记录表格**
```markdown
# 实验结果记录

## 优先级1: 因子分解优化

| 实验ID | 参数配置 | Valid Recall@20 | Test Recall@20 | 提升幅度 | 状态 |
|--------|----------|----------------|----------------|----------|------|
| 1.1.1 | factor_dim=16 | - | - | - | 待执行 |
| 1.1.2 | factor_dim=21 | 0.1013 | 0.1029 | 基准 | ✅ 基准 |
| 1.1.3 | factor_dim=24 | - | - | - | 待执行 |
| 1.1.4 | factor_dim=28 | - | - | - | 待执行 |

## 优先级2: 注意力机制优化

| 实验ID | 参数配置 | Valid Recall@20 | Test Recall@20 | 提升幅度 | 状态 |
|--------|----------|----------------|----------------|----------|------|
| 2.1.1 | attention_temp=0.5 | - | - | - | 待执行 |
| 2.1.2 | attention_temp=0.8 | - | - | - | 待执行 |

## 优先级3: 隐式学习增强

| 实验ID | 参数配置 | Valid Recall@20 | Test Recall@20 | 提升幅度 | 状态 |
|--------|----------|----------------|----------------|----------|------|
| 3.1.1 | enhanced_attr_mlp | - | - | - | 待执行 |
```

### **代码修改示例**

#### **示例1: 因子维度优化实现**
```python
# 修改 SOIL-main/models/soil.py 第200-250行区域
class SOIL(nn.Module):
    def __init__(self, config, dataset):
        super(SOIL, self).__init__()
        # ... 其他初始化代码 ...

        # 原始代码
        # self.factor_dim = self.embedding_size // self.n_factors

        # 优化代码: 支持自定义因子维度
        self.factor_dim = getattr(config, 'factor_dim', self.embedding_size // self.n_factors)

        # 确保总维度匹配
        total_factor_dim = self.factor_dim * self.n_factors
        if total_factor_dim != self.embedding_size:
            print(f"Warning: factor_dim({self.factor_dim}) * n_factors({self.n_factors}) = {total_factor_dim} != embedding_size({self.embedding_size})")
            # 可以选择调整或报错
```

#### **示例2: 注意力温度参数实现**
```python
# 修改 SOIL-main/models/soil.py 第402行
class SOIL(nn.Module):
    def __init__(self, config, dataset):
        # ... 其他初始化代码 ...
        self.attention_temperature = getattr(config, 'attention_temperature', 1.0)

    def forward(self, norm_adj, train=True):
        # ... 前向传播代码 ...

        # 原始代码
        # weights = self.modality_attention(factor_input)

        # 优化代码: 添加温度参数
        attention_logits = self.modality_attention(factor_input)
        weights = F.softmax(attention_logits / self.attention_temperature, dim=1)
```

---

## 📋 **实验检查清单**

### **实验前检查**
- [ ] 确认当前最优配置已保存
- [ ] 验证代码修改的正确性
- [ ] 准备实验记录表格
- [ ] 确保有足够的计算资源

### **实验中监控**
- [ ] 训练损失是否正常下降
- [ ] 验证性能是否按预期变化
- [ ] 是否出现训练不稳定现象
- [ ] 内存和GPU使用是否正常

### **实验后分析**
- [ ] 记录详细的性能数据
- [ ] 分析性能变化的原因
- [ ] 确定是否继续下一个实验
- [ ] 更新最优配置记录

---

**文档创建时间**: 2025年5月26日
**基于版本**: Enhanced SOIL v2.0
**优化目标**: 阶段1性能提升0.5-1.0%
**技术支持**: 完整的实现指导和监控工具
